package com.moego.server.grooming.server;

import static com.moego.common.enums.ServiceItemEnum.convertBitValueList;
import static com.moego.server.grooming.constant.AppointmentStatusSet.ACTIVE_STATUS_SET;
import static java.util.Comparator.comparing;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.minBy;
import static java.util.stream.Collectors.partitioningBy;
import static java.util.stream.Collectors.toCollection;
import static java.util.stream.Collectors.toMap;
import static org.springframework.util.CollectionUtils.isEmpty;

import com.github.pagehelper.PageInfo;
import com.moego.common.dto.AddressLatLng;
import com.moego.common.dto.PageDTO;
import com.moego.common.dto.clients.BusinessClientsDTO;
import com.moego.common.dto.clients.BusinessDateClientsDTO;
import com.moego.common.dto.clients.ClientsFilterDTO;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.common.exception.CommonException;
import com.moego.common.response.ResponseResult;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderRequest;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderResponse.EstimatedOrder;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.server.grooming.api.IGroomingAppointmentServiceBase;
import com.moego.server.grooming.convert.AppointmentConverter;
import com.moego.server.grooming.dto.AppointmentDTO;
import com.moego.server.grooming.dto.AppointmentListDto;
import com.moego.server.grooming.dto.AppointmentReminderSendDTO;
import com.moego.server.grooming.dto.AppointmentWithPetDetailsDto;
import com.moego.server.grooming.dto.BusinessUpcomingDTO;
import com.moego.server.grooming.dto.CustomerAppointmentNumInfoDTO;
import com.moego.server.grooming.dto.CustomerAppointmentWithAmountList;
import com.moego.server.grooming.dto.CustomerApptCountDTO;
import com.moego.server.grooming.dto.CustomerApptDateDTO;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentDTO;
import com.moego.server.grooming.dto.CustomerGroomingAppointmentWithAmountDTO;
import com.moego.server.grooming.dto.CustomerLastFinishedApptMapDto;
import com.moego.server.grooming.dto.CustomerLastServiceDTO;
import com.moego.server.grooming.dto.CustomerNextLastApptMapDto;
import com.moego.server.grooming.dto.CustomerRebookReminderDTO;
import com.moego.server.grooming.dto.CustomerUpComingAppointDTO;
import com.moego.server.grooming.dto.ExpiryCustomerCountListDto;
import com.moego.server.grooming.dto.GroomingBookingDTO;
import com.moego.server.grooming.dto.GroomingTicketWindowDetailDTO;
import com.moego.server.grooming.dto.InitDataGroomingResultDto;
import com.moego.server.grooming.dto.MoeGroomingAppointmentDTO;
import com.moego.server.grooming.dto.PetLastServiceDTO;
import com.moego.server.grooming.dto.waitlist.WaitlistAvailableDTO;
import com.moego.server.grooming.enums.AppointmentSourcePlatform;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.enums.PetDetailStatusEnum;
import com.moego.server.grooming.mapper.AppointmentMapperProxy;
import com.moego.server.grooming.mapper.MoeGroomingServiceOperationMapper;
import com.moego.server.grooming.mapper.PetDetailMapperProxy;
import com.moego.server.grooming.mapper.po.BusinessCountPO;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointment;
import com.moego.server.grooming.mapperbean.MoeGroomingAppointmentExample;
import com.moego.server.grooming.mapperbean.MoeGroomingInvoice;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetail;
import com.moego.server.grooming.mapperbean.MoeGroomingPetDetailExample;
import com.moego.server.grooming.mapperbean.MoeGroomingServiceOperationExample;
import com.moego.server.grooming.mapstruct.AppointmentMapper;
import com.moego.server.grooming.params.AdminQueryAppointmentParams;
import com.moego.server.grooming.params.AppointReminderParams;
import com.moego.server.grooming.params.AppointmentReminderSendParams;
import com.moego.server.grooming.params.ByCustomerIdsParam;
import com.moego.server.grooming.params.CancelParams;
import com.moego.server.grooming.params.ClientCustomerUpcomingParams;
import com.moego.server.grooming.params.CommonIdsParams;
import com.moego.server.grooming.params.ConfirmParams;
import com.moego.server.grooming.params.CustomerDeleteParams;
import com.moego.server.grooming.params.DateRangeParams;
import com.moego.server.grooming.params.GetInvoiceIdsAppointmentDateBetweenParam;
import com.moego.server.grooming.params.GroomingInitDataParam;
import com.moego.server.grooming.params.QueryCustomerApptNumPara;
import com.moego.server.grooming.params.QueryReminderRebookParams;
import com.moego.server.grooming.params.QuerySpecificSupportReminderRebookParams;
import com.moego.server.grooming.params.QueryUpcomingApptParams;
import com.moego.server.grooming.params.RepeatReminderParams;
import com.moego.server.grooming.params.status.StatusUpdateParams;
import com.moego.server.grooming.service.AppointmentServiceDetailService;
import com.moego.server.grooming.service.InitDataService;
import com.moego.server.grooming.service.MoeAppointmentQueryService;
import com.moego.server.grooming.service.MoeGroomingAppointmentService;
import com.moego.server.grooming.service.OrderService;
import com.moego.server.grooming.service.StaffAppointmentService;
import com.moego.server.grooming.service.WaitListService;
import jakarta.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequiredArgsConstructor
public class GroomingAppointmentServer extends IGroomingAppointmentServiceBase {

    private final MoeGroomingAppointmentService appointmentService;
    private final MoeAppointmentQueryService appointmentQueryService;
    private final OrderService orderService;
    private final InitDataService initDataService;
    private final WaitListService waitListService;
    private final AppointmentMapperProxy appointmentMapper;
    private final StaffAppointmentService staffAppointmentService;
    private final MigrateHelper migrateHelper;
    private final PetDetailMapperProxy petDetailMapper;
    private final MoeGroomingServiceOperationMapper serviceOperationMapper;
    private final AppointmentServiceDetailService serviceDetailService;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessStub;

    @Override
    public List<AppointmentDTO> list(Collection<Integer> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return List.of();
        }

        MoeGroomingAppointmentExample example = new MoeGroomingAppointmentExample();
        example.createCriteria().andIdIn(List.copyOf(Set.copyOf(ids)));
        // DONE: BD
        return appointmentMapper.selectByExample(example).stream()
                .map(AppointmentMapper.INSTANCE::entity2DTO)
                .toList();
    }

    /**
     * 用于商户创建，初始化customer模块数据
     *
     * @param initDataParam
     * @return
     */
    @Override
    public InitDataGroomingResultDto groomingInitData(@RequestBody GroomingInitDataParam initDataParam) {
        return initDataService.initGroomingModule(initDataParam);
    }

    /**
     * 初始化datarule
     *
     * @param businessId
     */
    @Override
    public void initDataRule(@RequestParam("businessId") Integer businessId) {}

    @Override
    public List<GroomingBookingDTO> queryNeedSendCalendarReminderAppt(
            @RequestParam Integer businessId, @RequestParam Integer staffId, @RequestParam Integer beforeMins) {
        return appointmentQueryService.queryNeedSendCalendarReminderAppt(businessId, staffId, beforeMins);
    }

    @Override
    public AppointmentReminderSendDTO queryAppointmentWithDetail(
            @RequestParam Integer businessId, @RequestParam Integer groomingId) {
        return appointmentQueryService.queryAppointmentWithDetail(businessId, groomingId);
    }

    @Override
    public ExpiryCustomerCountListDto queryExpiryCustomerList(
            @RequestParam Integer businessId,
            @RequestParam Integer upcomingNum,
            @RequestParam Integer pageNum,
            @RequestParam Integer pageSize,
            @RequestBody List<Integer> dismissIds) {
        return appointmentQueryService.queryExpiryCustomerList(businessId, upcomingNum, pageNum, pageSize, dismissIds);
    }

    @Override
    public Integer queryExpiryCustomerListCount(
            @RequestParam Integer businessId,
            @RequestParam Integer upcomingNum,
            @RequestBody List<Integer> dismissIds) {
        return appointmentQueryService.queryExpiryCustomerListCount(businessId, upcomingNum, dismissIds);
    }

    @Override
    public List<Integer> queryBusinessUpcomingApptIdList(@Valid @RequestBody QueryUpcomingApptParams params) {
        return appointmentQueryService.queryUpcomingApptIdList(params);
    }

    @Override
    public List<Integer> queryCustomerIdUpcomingApptIdList(
            @RequestParam Integer businessId, @RequestParam Integer customerId) {
        return appointmentQueryService.queryCustomerIdUpcomingApptIdList(businessId, customerId);
    }

    @Override
    public Map<Integer, Integer> countUpcomingApptByCustomerIds(ByCustomerIdsParam param) {
        if (ObjectUtils.isEmpty(param.getCustomerIds())) {
            return Map.of();
        }
        return appointmentQueryService.countUpcomingApptCountForCustomers(
                param.getBusinessId(), param.getCustomerIds());
    }

    /**
     * 查询customerList last和next预约
     *
     * @param customerIds
     * @return
     */
    public Map<Integer, CustomerGroomingAppointmentDTO> getAllCustomerLastAppointment(
            @RequestParam Integer businessId, @RequestBody CommonIdsParams customerIds) {

        var migrateInfo = migrateHelper.getMigrationInfo(businessId);
        List<Integer> customerIdList = null;
        if (customerIds != null && !isEmpty(customerIds.getIds())) {
            customerIdList = customerIds.getIds();
        }
        return appointmentQueryService.getAllCustomerLastAppointment(
                migrateInfo.isMigrate(), migrateInfo.companyId(), businessId, customerIdList);
    }

    public CustomerLastFinishedApptMapDto getCustomerLastFinishedAppointmentByIds(
            @RequestParam Integer businessId, @RequestBody CommonIdsParams customerIds) {
        var migrateInfo = migrateHelper.getMigrationInfo(businessId);
        return appointmentQueryService.getCustomerLastFinishedAppointment(
                migrateInfo.isMigrate(), migrateInfo.companyId(), businessId, customerIds.getIds());
    }

    /**
     * 查询customer最近未取消(confirmed/unconfirmed/finished)的appointment间隔/天
     * 同一天有多个appt算一个
     *
     * @param businessId 商家id
     * @param customerId customerId
     * @return
     */
    @Override
    public Integer getCustomerLastAppointmentInterval(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId) {
        return appointmentQueryService.getCustomerLastAppointmentInterval(businessId, customerId);
    }

    @Override
    public Map<Integer, Integer> getApptIntervalByCustomerIds(ByCustomerIdsParam param) {
        if (ObjectUtils.isEmpty(param.getCustomerIds())) {
            return Map.of();
        }
        return appointmentQueryService.getApptIntervalByCustomerIds(param.getBusinessId(), param.getCustomerIds());
    }

    @Override
    public Set<Integer> queryStaffIdListByDateRange(
            @RequestParam Integer businessId, @RequestBody DateRangeParams dateRangeParams) {
        if (businessId == null
                || dateRangeParams == null
                || StringUtils.isEmpty(dateRangeParams.getEndDate())
                || StringUtils.isEmpty(dateRangeParams.getStartDate())) {
            return Collections.emptySet();
        }
        return appointmentQueryService.queryStaffIdListByDateRange(businessId, dateRangeParams);
    }

    @Override
    public AddressLatLng convert(String fullAddress) {
        return null;
        // return googleMapService.queryCoordinateByAddress(fullAddress);
    }

    @Override
    public PageDTO<MoeGroomingAppointmentDTO> queryAppointmentReminder(
            @RequestBody AppointReminderParams appointReminderParams) {
        return appointmentQueryService.queryAppointmentReminder(appointReminderParams);
    }

    @Override
    public List<AppointmentReminderSendDTO> queryAppointmentReminderWill(
            @RequestBody AppointmentReminderSendParams appointmentReminderSendParams) {
        return appointmentQueryService.queryAppointmentReminderWill(appointmentReminderSendParams);
    }

    @Override
    public List<AppointmentReminderSendDTO> queryAppointmentReminderWithDetailWill(
            @RequestBody AppointmentReminderSendParams appointmentReminderSendParams) {
        return appointmentQueryService.queryAppointmentReminderWithDetailWill(appointmentReminderSendParams, false);
    }

    @Override
    public Map<String, CustomerGroomingAppointmentDTO> getCustomerLastAndNextAppointment(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam(value = "ignoreGroomingId", required = false) Integer ignoreGroomingId) {
        return appointmentQueryService.getCustomerLastAndNextAppointment(
                customerId, businessId, ignoreGroomingId, false);
    }

    @Override
    public Map<String, CustomerGroomingAppointmentDTO> getCustomerLastAndNextAppointmentV2(
            Integer businessId, Integer customerId, Integer ignoreGroomingId, boolean excludeBookingRequest) {
        return appointmentQueryService.getCustomerLastAndNextAppointment(
                customerId, businessId, ignoreGroomingId, excludeBookingRequest);
    }

    @Override
    public Map<Integer, CustomerGroomingAppointmentDTO> getCustomerNextAppointment(
            @RequestParam("businessId") Integer businessId, @RequestBody CommonIdsParams customerIds) {
        return appointmentQueryService.getCustomerNextAppointment(businessId, customerIds.getIds());
    }

    @Override
    public CustomerNextLastApptMapDto getCustomerNextLastAppointmentByIds(
            @RequestParam("businessId") Integer businessId, @RequestBody CommonIdsParams customerIds) {
        var migrateInfo = migrateHelper.getMigrationInfo(businessId);
        return appointmentQueryService.getCustomerNextLastAppointment(
                migrateInfo.isMigrate(), migrateInfo.companyId(), businessId, customerIds.getIds());
    }

    @Override
    public List<CustomerAppointmentNumInfoDTO> getCustomerAppointmentNum(
            @RequestParam("businessId") Integer businessId, @RequestBody List<Integer> customerIds) {
        var migrateInfo = migrateHelper.getMigrationInfo(businessId);
        return appointmentQueryService.getCustomerAppointmentNum(
                migrateInfo.isMigrate(), migrateInfo.companyId(), businessId, customerIds);
    }

    @Override
    public List<CustomerAppointmentNumInfoDTO> listCustomerAppointmentNum(Long companyId, List<Integer> customerIds) {
        return appointmentQueryService.getCustomerAppointmentNum(true, companyId, null, customerIds);
    }

    @Override
    public Map<Integer, List<CustomerAppointmentNumInfoDTO>> getCustomerAppointmentNumGroupByCustomerId(
            @RequestBody QueryCustomerApptNumPara apptNumPara) {
        var migrateInfo = migrateHelper.getMigrationInfo(apptNumPara.getBusinessId());

        return appointmentQueryService.getCustomerAppointmentNumGroupByCustomerId(
                migrateInfo.isMigrate(),
                migrateInfo.companyId(),
                apptNumPara.getBusinessId(),
                apptNumPara.getCustomerIds());
    }

    @Override
    public Set<Integer> listCustomerIdByCountFilter(ClientsFilterDTO clientsFilterDTO) {
        return appointmentQueryService.listCustomerIdByCountFilter(clientsFilterDTO);
    }

    @Override
    public Set<Integer> listCustomerIdByDateFilter(ClientsFilterDTO clientsFilterDTO) {
        return appointmentQueryService.listCustomerIdByDateFilter(clientsFilterDTO);
    }

    @Override
    public Set<Integer> listCustomerIdByGroomerFilter(ClientsFilterDTO clientsFilterDTO) {
        return appointmentQueryService.listCustomerIdByGroomerFilter(clientsFilterDTO);
    }

    @Override
    public Map<Integer, CustomerApptCountDTO> listCustomerApptCount(BusinessDateClientsDTO businessDateClientsDTO) {
        return appointmentQueryService.listCustomerApptCount(businessDateClientsDTO);
    }

    @Override
    public Map<Integer, CustomerApptDateDTO> listCustomerApptDate(BusinessDateClientsDTO businessDateClientsDTO) {
        return appointmentQueryService.listCustomerApptDate(businessDateClientsDTO);
    }

    @Override
    public Boolean customerDelete(@RequestBody CustomerDeleteParams deleteParams) {
        return appointmentService.customerDelete(deleteParams);
    }

    @Override
    public Boolean batchDeleteByCustomerId(Integer staffId, BusinessClientsDTO businessClientsDTO) {
        return appointmentService.batchDeleteByCustomerId(staffId, businessClientsDTO);
    }

    @Override
    public List<Integer> queryCustomerIdsInWaiting(@RequestBody List<Integer> customerIds) {
        return appointmentQueryService.queryCustomerIdsInWaiting(customerIds);
    }

    @Override
    public List<Integer> queryFutureAppointmentCustomerIdList(@RequestParam("tokenBusinessId") Integer businessId) {
        if (businessId == null) {
            return Collections.emptyList();
        }
        return appointmentQueryService.queryFutureAppointmentCustomerIdList(businessId);
    }

    @Override
    public PageDTO<MoeGroomingAppointmentDTO> queryRepeatAppointmentReminder(
            @RequestBody RepeatReminderParams repeatReminderParams) {
        return appointmentQueryService.queryRepeatAppointmentReminder(repeatReminderParams);
    }

    @Override
    public ResponseResult<BusinessUpcomingDTO> queryBusinessUpComingAppoint(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam(value = "date", required = false) String date) {
        return appointmentQueryService.queryBusinessUpComingAppoint(tokenBusinessId, date);
    }

    @Override
    public ResponseResult<List<CustomerUpComingAppointDTO>> queryCustomerUpComingAppointUnEncode(
            @RequestParam("customerId") Integer customerId,
            @RequestParam("businessId") Integer businessId,
            @RequestParam("appointmentDate") String appointmentDate,
            @RequestParam("minutes") Integer minutes) {
        return appointmentQueryService.queryCustomerUpComingAppointUnEncode(
                customerId, businessId, appointmentDate, minutes);
    }

    @Override
    public List<CustomerUpComingAppointDTO> queryCustomerUpComingAppointForClientShare(
            @RequestParam("customerId") Integer customerId) {
        return appointmentQueryService.queryCustomerUpComingAppointForShare(customerId);
    }

    @Override
    public List<Integer> selectCustomerIdWithIn7DaysAppointment(
            @RequestParam("tokenBusinessId") Integer businessId, @RequestParam("tokenStaffId") Integer tokenStaffId) {
        return appointmentQueryService.selectCustomerIdWithInCustomDaysAppointment(businessId, tokenStaffId, 7, 7);
    }

    @Override
    public List<Integer> selectCustomerIdWithInCustomDaysAppointment(
            @RequestParam("tokenBusinessId") Integer businessId,
            @RequestParam("tokenStaffId") Integer tokenStaffId,
            @RequestParam(name = "beforeDay", required = false) Integer beforeDay,
            @RequestParam(name = "afterDay", required = false) Integer afterDay) {
        return appointmentQueryService.selectCustomerIdWithInCustomDaysAppointment(
                businessId, tokenStaffId, beforeDay, afterDay);
    }

    @Override
    public Integer selectCustomerIdIsWithInCustomDaysAppointment(
            @RequestParam("tokenBusinessId") Integer businessId,
            @RequestParam("tokenStaffId") Integer tokenStaffId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam(name = "beforeDay", required = false) Integer beforeDay,
            @RequestParam(name = "afterDay", required = false) Integer afterDay) {
        return appointmentQueryService.selectCustomerIdIsWithInCustomAppointmentDate(
                businessId, tokenStaffId, customerId, beforeDay, afterDay);
    }

    @Override
    public Integer selectCustomerIdIsWithIn7AppointmentDate(
            @RequestParam("tokenBusinessId") Integer businessId,
            @RequestParam("tokenStaffId") Integer tokenStaffId,
            @RequestParam("customerId") Integer customerId) {
        return appointmentQueryService.selectCustomerIdIsWithIn7AppointmentDate(businessId, tokenStaffId, customerId);
    }

    @Override
    public List<Integer> selectCustomerIdWithInAllDaysAppointment(
            @RequestParam("tokenBusinessId") Integer businessId, @RequestParam("tokenStaffId") Integer tokenStaffId) {
        return appointmentQueryService.selectCustomerIdWithInAllDaysAppointment(businessId, tokenStaffId);
    }

    @Override
    public Integer selectCustomerIdIsWithInAllAppointmentDate(
            @RequestParam("tokenBusinessId") Integer businessId,
            @RequestParam("tokenStaffId") Integer tokenStaffId,
            @RequestParam("customerId") Integer customerId) {
        return appointmentQueryService.selectCustomerIdIsWithInAllAppointmentDate(businessId, tokenStaffId, customerId);
    }

    @Override
    public List<Integer> getBusinessIdsOnSendDaily() {
        return appointmentQueryService.getBusinessIdsOnSendDaily();
    }

    @Override
    public List<CustomerGroomingAppointmentDTO> getCustomerLastAppointments(
            @RequestParam("businessId") Integer businessId, @RequestBody List<Integer> customerIds) {
        return appointmentQueryService.getCustomerLastAppointments(businessId, customerIds);
    }

    @Override
    public ResponseResult<CustomerLastServiceDTO> getCustomerLastService(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId, @RequestParam("customerId") Integer customerId) {
        return appointmentQueryService.getCustomerLastService(tokenBusinessId, customerId);
    }

    @Override
    public String getCustomerLastAlertNote(
            @RequestParam("businessId") Integer businessId, @RequestParam("customerId") Integer customerId) {
        return appointmentQueryService.getCustomerAlertNote(null, businessId, customerId);
    }

    @Override
    public PageDTO<CustomerRebookReminderDTO> getCustomerRebookReminderList(
            @RequestBody QueryReminderRebookParams queryReminderRebookParams) {
        return appointmentQueryService.getCustomerRebookReminderList(queryReminderRebookParams);
    }

    @Override
    public PageDTO<CustomerRebookReminderDTO> getSpecificSupportCustomerRebookReminderList(
            @RequestBody QuerySpecificSupportReminderRebookParams queryReminderRebookParams) {
        return appointmentQueryService.getSpecificSupportCustomerRebookReminderList(queryReminderRebookParams);
    }

    @Override
    public CustomerRebookReminderDTO getCustomerRebookReminder(
            @RequestParam("businessId") Integer businessId,
            @RequestParam("customerId") Integer customerId,
            @RequestParam("timezoneName") String timezoneName) {
        return appointmentQueryService.getCustomerRebookReminderByCustomerId(businessId, customerId, timezoneName);
    }

    @Override
    public List<GroomingBookingDTO> queryApptWithPetDetailByIds(@RequestBody List<Integer> groomingIds) {
        return appointmentQueryService.queryApptWithPetDetailByIds(groomingIds);
    }

    @Override
    public ResponseResult<GroomingTicketWindowDetailDTO> queryTicketDetailWithWindow(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId, @RequestParam("id") Integer id) {
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(tokenBusinessId);
        return appointmentQueryService.queryTicketDetailWithWindow(
                migrateInfo.companyId(), tokenBusinessId, id, migrateInfo.isMigrate());
    }

    @Override
    public ResponseResult<PetLastServiceDTO> getPetLastService(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId, @RequestParam("petId") Integer petId) {
        var migrateInfo = migrateHelper.getMigrationInfo(tokenBusinessId);
        var migrated = migrateInfo.isMigrate();
        var companyId = migrateInfo.companyId();
        return appointmentQueryService.getPetLastService(migrated, companyId, tokenBusinessId, petId);
    }

    @Override
    public AppointmentWithPetDetailsDto getAppointmentWithPetDetails(
            @RequestParam("appointmentId") Integer appointmentId) {
        return appointmentQueryService.getAppointmentWithPetDetails(appointmentId, false);
    }

    @Override
    public List<AppointmentWithPetDetailsDto> getAppointmentListWithPetDetails(
            @RequestParam(value = "includeCancelled", required = false, defaultValue = "false")
                    Boolean includeCancelled,
            @RequestBody List<Integer> appointmentIds) {
        return appointmentQueryService.getAppointmentListWithPetDetails(
                new HashSet<>(appointmentIds), includeCancelled);
    }

    @Override
    public ResponseResult<Integer> confirm(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam("tokenStaffId") Integer tokenStaffId,
            @RequestBody ConfirmParams editIdParams) {
        if (editIdParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }

        editIdParams.setBusinessId(tokenBusinessId);
        editIdParams.setAccountId(tokenStaffId);
        return appointmentService.editAppointmentConfirm(editIdParams);
    }

    @Override
    public ResponseResult<Integer> cancel(
            @RequestParam("tokenBusinessId") Integer tokenBusinessId,
            @RequestParam("tokenStaffId") Integer tokenStaffId,
            @RequestBody CancelParams editIdParams) {
        if (editIdParams.getId() == null) {
            throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "id is not null");
        }
        if (editIdParams.getNoShow() == null) {
            editIdParams.setNoShow(Byte.parseByte("2"));
        }

        editIdParams.setBusinessId(tokenBusinessId);
        editIdParams.setAccountId(tokenStaffId);
        editIdParams.setCheckFinishStatus(Boolean.TRUE);
        return ResponseResult.success(appointmentService.cancelAppointmentRepeat(
                editIdParams.getRepeatType(), tokenBusinessId, editIdParams));
    }

    @Override
    public ResponseResult<String> getCustomerUpcomingUrl(@RequestParam("customerId") Integer customerId) {
        return appointmentQueryService.getCustomerUpComingUrl(customerId);
    }

    @Override
    public CustomerUpComingAppointDTO getLatestClientCustomerUpcomingAppoint(
            @RequestBody ClientCustomerUpcomingParams upcomingParams) {
        return appointmentQueryService.getLatestClientCustomerUpcomingAppoint(upcomingParams);
    }

    @Override
    public List<CustomerUpComingAppointDTO> getClientCustomerUpcomingAppoint(
            @RequestBody ClientCustomerUpcomingParams upcomingParams) {
        return appointmentQueryService.getClientCustomerUpcomingAppointList(upcomingParams);
    }

    @Override
    public CustomerUpComingAppointDTO getClientUpcomingAppointById(
            @RequestParam("appointmentId") Integer appointmentId) {
        return appointmentQueryService.getClientCustomerUpcomingAppointById(appointmentId);
    }

    @Override
    public AppointmentListDto adminQueryAppointment(AdminQueryAppointmentParams param) {
        var dto = appointmentQueryService.adminQueryAppointment(param);
        return new AppointmentListDto(AppointmentConverter.INSTANCE.toDtoList(dto.getFirst()), dto.getSecond());
    }

    /**
     * 只能删除已经 cancel 的预约
     * @param appointmentId
     * @return
     */
    @Override
    public Boolean adminDeleteCanceledAppointment(Long appointmentId) {
        return appointmentService.adminDeleteCanceledAppointment(appointmentId);
    }

    @Override
    public MoeGroomingAppointmentDTO getLastedNotCanceledAppointment(
            @RequestParam Integer businessId, @RequestParam Integer customerId) {
        MoeGroomingAppointment appointment =
                appointmentQueryService.getLastedNotCanceledAppointment(businessId, customerId);
        if (appointment == null) {
            return null;
        }
        MoeGroomingAppointmentDTO dto = new MoeGroomingAppointmentDTO();
        BeanUtils.copyProperties(appointment, dto);
        return dto;
    }

    @Override
    public CustomerAppointmentWithAmountList getAppointmentIdsCreatedBetween(
            Integer businessId,
            Long startTime,
            Long endTime,
            CommonIdsParams customerIds,
            Integer pageNum,
            Integer pageSize) {
        if (customerIds == null
                || customerIds.getIds() == null
                || customerIds.getIds().isEmpty()) {
            return new CustomerAppointmentWithAmountList();
        }
        PageInfo<MoeGroomingAppointment> appointments = appointmentQueryService.getAppointmentsCreatedBetween(
                businessId, startTime, endTime, customerIds.getIds(), pageNum, pageSize);
        if (CollectionUtils.isEmpty(appointments.getList())) {
            return new CustomerAppointmentWithAmountList();
        }
        var companyId = businessStub
                .getCompanyId(GetCompanyIdRequest.newBuilder()
                        .setBusinessId(businessId)
                        .build())
                .getCompanyId();
        var estimatedOrders = appointmentStub
                .previewEstimateOrder(PreviewEstimateOrderRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addAllAppointmentIds(appointments.getList().stream()
                                .map(appt -> appt.getId().longValue())
                                .toList())
                        .build())
                .getEstimatedOrdersList();
        Map<Long, BigDecimal> invoiceMap = estimatedOrders.stream()
                .collect(toMap(
                        EstimatedOrder::getAppointmentId, o -> MoneyUtils.fromGoogleMoney(o.getServicesSubtotal())));
        // 合并两个 list
        List<CustomerGroomingAppointmentWithAmountDTO> result = new ArrayList<>();
        for (MoeGroomingAppointment appointment : appointments.getList()) {
            CustomerGroomingAppointmentWithAmountDTO dto = new CustomerGroomingAppointmentWithAmountDTO();
            dto.setCustomerId(appointment.getCustomerId());
            dto.setApptId(appointment.getId());
            dto.setAmount(invoiceMap.getOrDefault(appointment.getId().longValue(), BigDecimal.ZERO));
            dto.setCreateTime(appointment.getCreateTime());
            result.add(dto);
        }

        CustomerAppointmentWithAmountList list = new CustomerAppointmentWithAmountList();
        list.setAppts(result);
        list.setCount(appointments.getTotal());
        return list;
    }

    @Override
    public AppointmentDTO getAppointmentById(int appointmentId) {
        MoeGroomingAppointment appt = appointmentMapper.selectByPrimaryKey(appointmentId);
        return AppointmentMapper.INSTANCE.entity2DTO(appt);
    }

    @Override
    public MoeGroomingAppointmentDTO getNewCreateAppointment(Integer businessId, Integer customerId) {
        return appointmentQueryService.getCustomerNewAppointment(businessId, customerId);
    }

    @Override
    public Set<Integer> getInvoiceIdsAppointmentHasProductAndDateBetween(
            GetInvoiceIdsAppointmentDateBetweenParam param) {
        List<Integer> apptIds = appointmentMapper.getAppointmentsDateBetween(param).stream()
                .map(MoeGroomingAppointment::getId)
                .toList();
        return orderService
                .getListByGroomingIds(param.getBusinessId(), apptIds, OrderSourceType.APPOINTMENT.getSource())
                .stream()
                .filter(invoice -> Boolean.TRUE.equals(invoice.getHasProduct()))
                .map(MoeGroomingInvoice::getId)
                .collect(Collectors.toSet());
    }

    @Override
    public void updateReadyNotificationResult(Integer appointmentId, String failedReason) {
        appointmentService.updateStatusForPickupNotification(appointmentId, failedReason);
    }

    @Override
    public Set<Integer> getStaffIdListAssignedAppointment(Integer businessId, String startDate, String endDate) {
        return staffAppointmentService.getClientListAssignedAppointment(businessId, startDate, endDate);
    }

    @Override
    public Map<String, Set<Integer>> getStaffAssignedAppointmentByDate(
            Integer businessId, String startDate, String endDate) {
        return staffAppointmentService.getStaffAssignedAppointmentsByDate(businessId, startDate, endDate);
    }

    @Override
    public Map<Integer, Long> countAppointmentFromGoogle(List<Integer> businessIds) {
        if (ObjectUtils.isEmpty(businessIds)) {
            return Map.of();
        }
        return appointmentMapper
                .countAppointmentFromSourcePlatform(businessIds, AppointmentSourcePlatform.RESERVE_WITH_GOOGLE.name())
                .stream()
                .collect(Collectors.toMap(BusinessCountPO::getBusinessId, BusinessCountPO::getCount));
    }

    @Override
    public int updateStatus(Integer tokenBusinessId, Integer tokenStaffId, StatusUpdateParams editIdParams) {
        return appointmentService
                        .updateAppointmentStatus(tokenBusinessId, tokenStaffId, editIdParams)
                        .isStatusUpdated()
                ? 1
                : 0;
    }

    @Override
    public List<Integer> getAppointmentRelatedStaffIds(@RequestParam("appointmentId") Integer appointmentId) {
        if (appointmentId == null || appointmentId <= 0) {
            return List.of();
        }
        return new ArrayList<>(appointmentQueryService.getAppointmentRelatedStaffIds(appointmentId));
    }

    @Override
    public boolean hasAppointment(HasAppointmentParam param) {
        List<Integer> appointmentIds =
                appointmentMapper.listActiveAppointmentIdByCustomerId(param.businessId(), param.customerId());
        if (ObjectUtils.isEmpty(appointmentIds)) {
            return false;
        }

        return !serviceDetailService
                .appointmentsFilterByStaff(appointmentIds, param.staffId())
                .isEmpty();
    }

    @Override
    public Map<Integer, List<AppointmentDTO>> listGroupedAppointmentByDateRange(
            ListGroupedAppointmentByDateRangeParam param) {

        Map<Integer, List<AppointmentDTO>> serviceTypeToAppointments = new HashMap<>();

        Map</*hasMultiTypeService*/ Boolean, List<AppointmentDTO>> hasMultiTypeServiceToAppointments =
                listGroupedAppointment(param);

        // single service type appointments
        serviceTypeToAppointments.putAll(hasMultiTypeServiceToAppointments.getOrDefault(false, List.of()).stream()
                .collect(groupingBy(
                        GroomingAppointmentServer::getServiceItemType,
                        toCollection(ArrayList::new)))); // 使用 mutable list

        // multi service type appointments
        Map<Integer, AppointmentDTO> apptIdToAppt =
                hasMultiTypeServiceToAppointments.getOrDefault(true, List.of()).stream()
                        .collect(toMap(AppointmentDTO::getId, identity()));

        var apptIdToPrimaryPetDetail = listPrimaryPetDetail(apptIdToAppt.keySet());
        apptIdToPrimaryPetDetail.forEach(
                (apptId, petDetailOpt) -> petDetailOpt.ifPresent(petDetail -> serviceTypeToAppointments
                        .computeIfAbsent(petDetail.getServiceItemType(), k -> new ArrayList<>())
                        .add(apptIdToAppt.get(apptId))));

        return serviceTypeToAppointments;
    }

    @Override
    public WaitlistAvailableDTO queryGroomingWaitlistAvailable(
            Integer companyId, Integer businessId, Integer pageNum, Integer pageSize) {
        return waitListService.queryGroomingWaitlistAvailable(companyId, businessId, pageNum, pageSize);
    }

    private static Integer getServiceItemType(AppointmentDTO e) {
        return Optional.ofNullable(convertBitValueList(e.getServiceTypeInclude()))
                .map(CollectionUtils::firstElement)
                .map(ServiceItemEnum::getServiceItem)
                .orElse(0); // shouldn't happen
    }

    private Map<Integer, Optional<MoeGroomingPetDetail>> listPrimaryPetDetail(Collection<Integer> appointmentIds) {
        if (ObjectUtils.isEmpty(appointmentIds)) {
            return Map.of();
        }

        MoeGroomingPetDetailExample example = new MoeGroomingPetDetailExample();
        example.createCriteria()
                .andGroomingIdIn(List.copyOf(appointmentIds))
                .andStatusEqualTo(PetDetailStatusEnum.NORMAL.getValue().byteValue());

        // 这里有个特别奇葩的逻辑：appointment 的类型由第一个 service 的类型决定
        // 我们认为 id 小的就是这个 appointment 的主 service :)，所以取 id 最小的那个
        // see
        // https://moego.atlassian.net/wiki/spaces/MO/pages/474218497/3.5+Early+access+-+May+followups+-+Part+1#%E7%BB%9F%E8%AE%A1%E6%96%B9%E6%A1%88

        return petDetailMapper.selectByExample(example).stream()
                .collect(
                        groupingBy(MoeGroomingPetDetail::getGroomingId, minBy(comparing(MoeGroomingPetDetail::getId))));
    }

    private Map<Boolean, List<AppointmentDTO>> listGroupedAppointment(ListGroupedAppointmentByDateRangeParam param) {
        var e = new MoeGroomingAppointmentExample();
        e.createCriteria()
                .andBusinessIdEqualTo(param.businessId())
                .andIsDeprecateEqualTo(0)
                .andIsBlockEqualTo(2)
                .andIsWaitingListEqualTo((byte) 0)
                .andBookOnlineStatusEqualTo((byte) 0)
                .andStatusIn(ACTIVE_STATUS_SET.stream()
                        .map(AppointmentStatusEnum::getValue)
                        .toList())
                .andAppointmentDateBetween(
                        LocalDate.parse(param.dateRange().lower()).minusDays(60).toString(),
                        param.dateRange().upper())
                .andAppointmentEndDateBetween(
                        param.dateRange().lower(),
                        LocalDate.parse(param.dateRange().upper()).plusDays(60).toString());

        return appointmentMapper.selectByExample(e).stream()
                .map(AppointmentMapper.INSTANCE::entity2DTO)
                .collect(partitioningBy(GroomingAppointmentServer::hasMultiServiceType));
    }

    private static boolean hasMultiServiceType(AppointmentDTO it) {
        // 如果 getServiceTypeInclude 是 2 的幂，说明只有一个 service type
        int n = it.getServiceTypeInclude();
        return (n & (n - 1)) != 0; // 0b1000(n) & 0b0111(n-1) = 0b0000
    }

    private boolean hasMultiStaffAppointment(HasAppointmentParam param, List<Integer> appointmentIds) {
        MoeGroomingServiceOperationExample example = new MoeGroomingServiceOperationExample();
        example.createCriteria()
                .andBusinessIdEqualTo(param.businessId())
                .andGroomingIdIn(appointmentIds)
                .andStaffIdEqualTo(param.staffId());
        return serviceOperationMapper.countByExample(example) > 0;
    }

    private boolean hasPrimaryStaffAppointment(HasAppointmentParam param, List<Integer> appointmentIds) {
        MoeGroomingPetDetailExample example = new MoeGroomingPetDetailExample();
        example.createCriteria()
                .andGroomingIdIn(appointmentIds)
                .andStaffIdEqualTo(param.staffId())
                .andStatusEqualTo(PetDetailStatusEnum.NORMAL.getValue().byteValue());
        return petDetailMapper.countByExample(example) > 0;
    }
}
