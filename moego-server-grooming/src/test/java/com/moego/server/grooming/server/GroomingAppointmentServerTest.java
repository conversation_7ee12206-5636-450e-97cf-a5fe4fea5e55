package com.moego.server.grooming.server;

import com.moego.api.common.Range;
import com.moego.server.grooming.api.IGroomingAppointmentService;
import java.time.LocalDate;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * {@link GroomingAppointmentServer} tester.
 */
@SpringBootTest
@Disabled("local test only")
class GroomingAppointmentServerTest {

    @Autowired
    private GroomingAppointmentServer server;

    /**
     * {@link GroomingAppointmentServer#listGroupedAppointmentByDateRange(IGroomingAppointmentService.ListGroupedAppointmentByDateRangeParam)}
     */
    @Test
    void testListGroupedAppointmentByDateRange() {
        server.listGroupedAppointmentByDateRange(new IGroomingAppointmentService.ListGroupedAppointmentByDateRangeParam(
                100611,
                new Range<>(
                        LocalDate.now().minusDays(30).toString(),
                        LocalDate.now().toString())));
    }
}
