package com.moego.server.grooming.service.ob;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.smart_scheduler.v1.TimeSlotType;
import com.moego.server.business.dto.AppointmentPetIdDTO;
import com.moego.server.business.dto.PetSizeDTO;
import com.moego.server.customer.client.IPetBreedClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.MoePetBreedDTO;
import com.moego.server.grooming.dto.LimitDto;
import com.moego.server.grooming.dto.LimitGroupDTO;
import com.moego.server.grooming.dto.PetBreedLimitDto;
import com.moego.server.grooming.dto.PetSizeLimitDto;
import com.moego.server.grooming.dto.ServiceLimitDto;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import com.moego.server.grooming.service.BookOnlinePetLimitService;
import com.moego.server.grooming.service.dto.ob.OBPetLimitFilterDTO;
import com.moego.server.grooming.service.dto.ob.PetAvailableDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class OBPetLimitServiceTest {

    @Mock
    private BookOnlinePetLimitService bookOnlinePetLimitService;

    @Mock
    private IPetClient iPetClient;

    @Mock
    private IPetBreedClient iPetBreedClient;

    @InjectMocks
    private OBPetLimitService obPetLimitService;

    /**
     * pet size limit 设置项
     *
     * @return
     */
    Map<Long, PetSizeDTO> buildPetSizeLimitMap() {
        Map<Long, PetSizeDTO> petSizeLimitMap = new HashMap<>();
        petSizeLimitMap.put(
                30001L,
                PetSizeDTO.builder()
                        .id(30001L)
                        .name("Large Size")
                        .weightLow(5)
                        .weightHigh(10)
                        .build());
        return petSizeLimitMap;
    }

    Map<Long, MoePetBreedDTO> buildPetBreedLimitMap() {
        Map<Long, MoePetBreedDTO> petBreedLimitMap = new HashMap<>();
        petBreedLimitMap.put(
                40001L,
                MoePetBreedDTO.builder().id(40001).name("Doodle").petTypeId(1).build());
        return petBreedLimitMap;
    }

    /**
     * 当前的预约
     *
     * @return
     */
    List<OBPetDataDTO> buildPetParamList() {
        List<OBPetDataDTO> petParamList = new ArrayList<>();
        OBPetDataDTO params1 = new OBPetDataDTO();
        params1.setWeight("10");
        params1.setBreed("Doodle");
        petParamList.add(params1);
        OBPetDataDTO params2 = new OBPetDataDTO();
        params2.setWeight("20");
        params2.setBreed("Doodle");
        petParamList.add(params2);
        return petParamList;
    }

    /**
     * 已有的预约
     *
     * @return
     */
    List<AppointmentPetIdDTO> buildPetIdDTOList() {
        List<AppointmentPetIdDTO> petIdDTOList = new ArrayList<>();
        petIdDTOList.add(AppointmentPetIdDTO.builder()
                .petId(98001)
                .staffId(57001)
                .appointmentDate("2022-10-28")
                .appointmentStartTime(600)
                .build());
        petIdDTOList.add(AppointmentPetIdDTO.builder()
                .petId(98002)
                .staffId(57001)
                .appointmentDate("2022-10-28")
                .appointmentStartTime(600)
                .build());
        return petIdDTOList;
    }

    public List<CustomerPetDetailDTO> buildPetIdDetailList() {
        List<CustomerPetDetailDTO> list = new ArrayList<>();
        CustomerPetDetailDTO petDetailDTO1 = new CustomerPetDetailDTO();
        petDetailDTO1.setPetId(98001);
        petDetailDTO1.setWeight("20");
        petDetailDTO1.setBreed("Doodle");
        list.add(petDetailDTO1);
        CustomerPetDetailDTO petDetailDTO2 = new CustomerPetDetailDTO();
        petDetailDTO2.setPetId(98002);
        petDetailDTO2.setWeight("20");
        petDetailDTO2.setBreed("Doodle");
        list.add(petDetailDTO2);
        CustomerPetDetailDTO petDetailDTO3 = new CustomerPetDetailDTO();
        petDetailDTO3.setPetId(98003);
        petDetailDTO3.setWeight("30");
        petDetailDTO3.setBreed("Poodle");
        list.add(petDetailDTO3);
        return list;
    }

    Map<Integer, MoePetBreedDTO> buildPetBreedMap() {
        Map<Integer, MoePetBreedDTO> petBreedMap = new HashMap<>();
        petBreedMap.put(
                40001,
                MoePetBreedDTO.builder().id(40001).name("Doodle").petTypeId(1).build());
        petBreedMap.put(
                40002,
                MoePetBreedDTO.builder().id(40002).name("Poodle").petTypeId(1).build());
        petBreedMap.put(
                40003,
                MoePetBreedDTO.builder()
                        .id(40003)
                        .name("Golden Doodle")
                        .petTypeId(1)
                        .build());
        petBreedMap.put(
                40004,
                MoePetBreedDTO.builder()
                        .id(40004)
                        .name("Chihuahua")
                        .petTypeId(1)
                        .build());
        return petBreedMap;
    }

    @Test
    void judgePetLimit() {
        when(iPetClient.getCustomerPetListByIdList(any())).thenAnswer(invocationOnMock -> buildPetIdDetailList());
        when(iPetBreedClient.getPetBreed(any())).thenAnswer(invocationOnMock -> buildPetBreedMap());

        OBPetLimitFilterDTO dto =
                obPetLimitService.generateWithCurrentAppointment(100794L, 100794, buildPetParamList(), List.of());
        dto = obPetLimitService.fillWithExistingAppointment(dto, buildPetIdDTOList());

        dto.setPetSizeMap(buildPetSizeLimitMap());
        dto.setPetBreedMap(buildPetBreedMap());
        LimitDto limitDto = new LimitDto();
        limitDto.getPetSizeLimitList()
                .add(PetSizeLimitDto.builder()
                        .petSizeIds(Collections.singletonList(30001L))
                        .isAllPetSize(true)
                        .capacity(10)
                        .build());
        boolean noMorePetQuantity = obPetLimitService.judgePetLimit(dto, 57001, "2022-10-28", limitDto, null);

        assertThat(noMorePetQuantity).isFalse();
    }

    @Test
    void generatePetLimitFilterDTO() {
        when(iPetClient.getCustomerPetListByIdList(any())).thenAnswer(invocationOnMock -> buildPetIdDetailList());
        when(iPetBreedClient.getPetBreed(any())).thenAnswer(invocationOnMock -> buildPetBreedMap());

        OBPetLimitFilterDTO dto =
                obPetLimitService.generateWithCurrentAppointment(100794L, 100794, buildPetParamList(), List.of());
        dto = obPetLimitService.fillWithExistingAppointment(dto, buildPetIdDTOList());

        assertThat(dto).isNotNull();
    }

    /**
     * 构建包含 null 列表的 LimitGroupDTO 用于测试 NullPointerException 防护
     */
    private List<LimitGroupDTO> buildLimitGroupsWithNullLists() {
        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup = new LimitGroupDTO();
        limitGroup.setOnlyAcceptSelected(false);

        // 故意设置为 null 来测试防护逻辑
        limitGroup.setServiceLimitList(null);
        limitGroup.setPetSizeLimitList(null);
        limitGroup.setPetBreedLimitList(null);

        limitGroups.add(limitGroup);
        return limitGroups;
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withNullLimitLists() {
        when(iPetClient.getCustomerPetListByIdList(any())).thenAnswer(invocationOnMock -> buildPetIdDetailList());
        when(iPetBreedClient.getPetBreed(any())).thenAnswer(invocationOnMock -> buildPetBreedMap());

        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = obPetLimitService.generateWithCurrentAppointment(
                100794L, 100794, buildPetParamListWithIndex(), List.of());
        obPetLimitFilterDTO = obPetLimitService.fillWithExistingAppointment(obPetLimitFilterDTO, buildPetIdDTOList());

        // 设置必要的映射数据
        obPetLimitFilterDTO.setPetSizeMap(buildPetSizeLimitMap());
        obPetLimitFilterDTO.setPetBreedMap(buildPetBreedMap());

        List<Set<Integer>> petIndexSubList = buildPetIndexSubList();

        Integer staffId = 57001;
        String date = "2022-10-28";
        List<LimitGroupDTO> limitGroups = buildLimitGroupsWithNullLists(); // 使用包含 null 列表的限制组
        Integer startTime = 600;

        // 执行测试 - 这应该不会抛出 NullPointerException
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 由于限制列表为 null，应该不会有限制生效，所以可用列表应该包含所有传入的子列表
        assertThat(result.getPetAvailableSubList()).isEqualTo(petIndexSubList);
    }

    /**
     * 构建 LimitGroupDTO 列表用于测试
     */
    private List<LimitGroupDTO> buildLimitGroups() {
        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup = new LimitGroupDTO();
        limitGroup.setOnlyAcceptSelected(false);

        // 添加服务限制
        List<ServiceLimitDto> serviceLimitList = new ArrayList<>();
        serviceLimitList.add(ServiceLimitDto.builder()
                .serviceIds(List.of(1001L, 1002L))
                .isAllService(false)
                .capacity(5)
                .build());
        limitGroup.setServiceLimitList(serviceLimitList);

        // 添加宠物大小限制
        List<PetSizeLimitDto> petSizeLimitList = new ArrayList<>();
        petSizeLimitList.add(PetSizeLimitDto.builder()
                .petSizeIds(List.of(30001L))
                .isAllPetSize(false)
                .capacity(3)
                .build());
        limitGroup.setPetSizeLimitList(petSizeLimitList);

        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList = new ArrayList<>();
        petBreedLimitList.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40001L, 40002L))
                .isAllBreed(false)
                .capacity(4)
                .build());
        limitGroup.setPetBreedLimitList(petBreedLimitList);

        limitGroups.add(limitGroup);
        return limitGroups;
    }

    /**
     * 构建 petIndexSubList 用于测试
     */
    private List<Set<Integer>> buildPetIndexSubList() {
        List<Set<Integer>> petIndexSubList = new ArrayList<>();

        Set<Integer> subList1 = new HashSet<>();
        subList1.add(0);
        subList1.add(1);
        petIndexSubList.add(subList1);

        Set<Integer> subList2 = new HashSet<>();
        subList2.add(0);
        petIndexSubList.add(subList2);

        Set<Integer> subList3 = new HashSet<>();
        subList3.add(1);
        petIndexSubList.add(subList3);

        return petIndexSubList;
    }

    /**
     * 构建带有 petIndex 的 OBPetDataDTO 列表
     */
    private List<OBPetDataDTO> buildPetParamListWithIndex() {
        List<OBPetDataDTO> petParamList = new ArrayList<>();

        OBPetDataDTO params1 = new OBPetDataDTO();
        params1.setPetIndex(0);
        params1.setWeight("10");
        params1.setBreed("Doodle");
        params1.setPetTypeId(1);
        petParamList.add(params1);

        OBPetDataDTO params2 = new OBPetDataDTO();
        params2.setPetIndex(1);
        params2.setWeight("20");
        params2.setBreed("Poodle");
        params2.setPetTypeId(1);
        petParamList.add(params2);

        return petParamList;
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withEmptyLimitGroups() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = buildPetIndexSubList();
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);

        Integer staffId = 57001;
        String date = "2022-10-28";
        List<LimitGroupDTO> limitGroups = Collections.emptyList(); // 空的限制组
        Integer startTime = 600;

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isTrue(); // 应该相等，因为传入的是同一个列表
        assertThat(result.getPetAvailableSubList()).isEqualTo(petIndexSubList);
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withEmptyPetIndexSubList() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        obPetLimitFilterDTO.setPetIndexSubList(buildPetIndexSubList());

        Integer staffId = 57001;
        String date = "2022-10-28";
        List<LimitGroupDTO> limitGroups = buildLimitGroups();
        Integer startTime = 600;
        List<Set<Integer>> petIndexSubList = Collections.emptyList(); // 空的宠物索引列表

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isFalse(); // 不相等，因为 obPetLimitFilterDTO 中有数据
        assertThat(result.getPetAvailableSubList()).isEqualTo(petIndexSubList);
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withBothEmpty() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        obPetLimitFilterDTO.setPetIndexSubList(Collections.emptyList());

        Integer staffId = 57001;
        String date = "2022-10-28";
        List<LimitGroupDTO> limitGroups = Collections.emptyList();
        Integer startTime = 600;
        List<Set<Integer>> petIndexSubList = Collections.emptyList();

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isTrue(); // 都为空，应该相等
        assertThat(result.getPetAvailableSubList()).isEqualTo(petIndexSubList);
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withDifferentPetIndexSubList() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> originalPetIndexSubList = buildPetIndexSubList();
        obPetLimitFilterDTO.setPetIndexSubList(originalPetIndexSubList);

        // 创建不同的 petIndexSubList
        List<Set<Integer>> differentPetIndexSubList = new ArrayList<>();
        Set<Integer> subList = new HashSet<>();
        subList.add(0);
        differentPetIndexSubList.add(subList);

        Integer staffId = 57001;
        String date = "2022-10-28";
        List<LimitGroupDTO> limitGroups = Collections.emptyList(); // 使用空限制组避免复杂逻辑
        Integer startTime = 600;

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, differentPetIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isFalse(); // 不相等，因为传入的列表不同
        assertThat(result.getPetAvailableSubList()).isEqualTo(differentPetIndexSubList);
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withSamePetIndexSubList() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = buildPetIndexSubList();
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);

        Integer staffId = 57001;
        String date = "2022-10-28";
        List<LimitGroupDTO> limitGroups = Collections.emptyList(); // 使用空限制组避免复杂逻辑
        Integer startTime = 600;

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isTrue(); // 相等，因为传入的是同一个列表
        assertThat(result.getPetAvailableSubList()).isEqualTo(petIndexSubList);
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withNormalCase() {
        when(iPetClient.getCustomerPetListByIdList(any())).thenAnswer(invocationOnMock -> buildPetIdDetailList());
        when(iPetBreedClient.getPetBreed(any())).thenAnswer(invocationOnMock -> buildPetBreedMap());

        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = obPetLimitService.generateWithCurrentAppointment(
                100794L, 100794, buildPetParamListWithIndex(), List.of());
        obPetLimitFilterDTO = obPetLimitService.fillWithExistingAppointment(obPetLimitFilterDTO, buildPetIdDTOList());

        // 设置必要的映射数据
        obPetLimitFilterDTO.setPetSizeMap(buildPetSizeLimitMap());
        obPetLimitFilterDTO.setPetBreedMap(buildPetBreedMap());

        List<Set<Integer>> petIndexSubList = buildPetIndexSubList();

        Integer staffId = 57001;
        String date = "2022-10-28";
        List<LimitGroupDTO> limitGroups = buildLimitGroups();
        Integer startTime = 600;

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withNullStartTime() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = buildPetIndexSubList();
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);

        Integer staffId = 57001;
        String date = "2022-10-28";
        List<LimitGroupDTO> limitGroups = Collections.emptyList();
        Integer startTime = null; // 测试 null 的情况

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isTrue();
        assertThat(result.getPetAvailableSubList()).isEqualTo(petIndexSubList);
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withOneLimitation_true() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = List.of(Set.of(0));
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);
        obPetLimitFilterDTO.setPetAppointmentMap(Map.of());
        obPetLimitFilterDTO.setPetIdDetailMap(Map.of());
        obPetLimitFilterDTO.setPetBreedMap(Map.of(
                40001,
                        MoePetBreedDTO.builder()
                                .id(40001)
                                .name("Doodle")
                                .petTypeId(1)
                                .build(),
                40002,
                        MoePetBreedDTO.builder()
                                .id(40002)
                                .name("Poodle")
                                .petTypeId(1)
                                .build(),
                40003,
                        MoePetBreedDTO.builder()
                                .id(40003)
                                .name("Golden Doodle")
                                .petTypeId(1)
                                .build()));
        obPetLimitFilterDTO.setPetSizeMap(Map.of(
                30001L,
                        PetSizeDTO.builder()
                                .id(1L)
                                .name("Small")
                                .weightLow(0)
                                .weightHigh(5)
                                .build(),
                30002L,
                        PetSizeDTO.builder()
                                .id(2L)
                                .name("Medium")
                                .weightLow(6)
                                .weightHigh(10)
                                .build(),
                30003L,
                        PetSizeDTO.builder()
                                .id(3L)
                                .name("Large")
                                .weightLow(11)
                                .weightHigh(15)
                                .build()));
        obPetLimitFilterDTO.setServiceMap(Map.of(
                1001L,
                        ServiceModel.newBuilder()
                                .setServiceId(1L)
                                .setName("Service 1")
                                .build(),
                1002L,
                        ServiceModel.newBuilder()
                                .setServiceId(2L)
                                .setName("Service 2")
                                .build()));

        OBPetDataDTO petData1 = new OBPetDataDTO();
        petData1.setWeight("10");
        petData1.setPetTypeId(1);
        petData1.setBreed("Doodle");
        obPetLimitFilterDTO.setPetIndexMap(Map.of(0, petData1));

        Integer staffId = 57001;
        String date = "2022-10-28";
        Integer startTime = null;

        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup = new LimitGroupDTO();
        limitGroup.setOnlyAcceptSelected(false);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList = new ArrayList<>();
        petBreedLimitList.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40001L, 40002L))
                .isAllBreed(false)
                .capacity(1)
                .build());
        limitGroup.setPetBreedLimitList(petBreedLimitList);
        limitGroups.add(limitGroup);

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isTrue();
        assertThat(result.getPetAvailableSubList()).isEqualTo(petIndexSubList);
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withOneBreedLimitation_true() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = List.of(Set.of(0));
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);
        obPetLimitFilterDTO.setPetAppointmentMap(Map.of());
        obPetLimitFilterDTO.setPetIdDetailMap(Map.of());
        obPetLimitFilterDTO.setPetBreedMap(Map.of(
                40001,
                MoePetBreedDTO.builder().id(40001).name("Doodle").petTypeId(1).build(),
                40002,
                MoePetBreedDTO.builder().id(40002).name("Poodle").petTypeId(1).build(),
                40003,
                MoePetBreedDTO.builder()
                        .id(40003)
                        .name("Golden Doodle")
                        .petTypeId(1)
                        .build(),
                40011,
                MoePetBreedDTO.builder()
                        .id(40011)
                        .name("Abyssinian")
                        .petTypeId(2)
                        .build()));
        obPetLimitFilterDTO.setPetSizeMap(Map.of(
                30001L,
                PetSizeDTO.builder()
                        .id(1L)
                        .name("Small")
                        .weightLow(0)
                        .weightHigh(5)
                        .build(),
                30002L,
                PetSizeDTO.builder()
                        .id(2L)
                        .name("Medium")
                        .weightLow(6)
                        .weightHigh(10)
                        .build(),
                30003L,
                PetSizeDTO.builder()
                        .id(3L)
                        .name("Large")
                        .weightLow(11)
                        .weightHigh(15)
                        .build()));
        obPetLimitFilterDTO.setServiceMap(Map.of(
                1001L,
                ServiceModel.newBuilder().setServiceId(1L).setName("Service 1").build(),
                1002L,
                ServiceModel.newBuilder().setServiceId(2L).setName("Service 2").build()));

        OBPetDataDTO petData1 = new OBPetDataDTO();
        petData1.setWeight("10");
        petData1.setPetTypeId(1);
        petData1.setBreed("Doodle");
        obPetLimitFilterDTO.setPetIndexMap(Map.of(0, petData1));

        Integer staffId = 57001;
        String date = "2022-10-28";
        Integer startTime = null;

        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup = new LimitGroupDTO();
        limitGroup.setOnlyAcceptSelected(false);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList = new ArrayList<>();
        petBreedLimitList.add(PetBreedLimitDto.builder()
                .petTypeId(2L)
                .breedIds(List.of())
                .isAllBreed(true)
                .capacity(0)
                .build());
        limitGroup.setPetBreedLimitList(petBreedLimitList);
        limitGroups.add(limitGroup);

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isTrue();
        assertThat(result.getPetAvailableSubList()).isEqualTo(petIndexSubList);
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withOneServiceLimitation_withAddon_true() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = List.of(Set.of(0));
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);
        obPetLimitFilterDTO.setPetAppointmentMap(Map.of());
        obPetLimitFilterDTO.setPetIdDetailMap(Map.of());
        obPetLimitFilterDTO.setPetBreedMap(Map.of(
                40001,
                MoePetBreedDTO.builder().id(40001).name("Doodle").petTypeId(1).build(),
                40002,
                MoePetBreedDTO.builder().id(40002).name("Poodle").petTypeId(1).build(),
                40003,
                MoePetBreedDTO.builder()
                        .id(40003)
                        .name("Golden Doodle")
                        .petTypeId(1)
                        .build(),
                40011,
                MoePetBreedDTO.builder()
                        .id(40011)
                        .name("Abyssinian")
                        .petTypeId(2)
                        .build()));
        obPetLimitFilterDTO.setPetSizeMap(Map.of(
                30001L,
                PetSizeDTO.builder()
                        .id(1L)
                        .name("Small")
                        .weightLow(0)
                        .weightHigh(5)
                        .build(),
                30002L,
                PetSizeDTO.builder()
                        .id(2L)
                        .name("Medium")
                        .weightLow(6)
                        .weightHigh(10)
                        .build(),
                30003L,
                PetSizeDTO.builder()
                        .id(3L)
                        .name("Large")
                        .weightLow(11)
                        .weightHigh(15)
                        .build()));
        obPetLimitFilterDTO.setServiceMap(Map.of(
                1001L,
                ServiceModel.newBuilder()
                        .setServiceId(1L)
                        .setType(ServiceType.SERVICE)
                        .setName("Service 1")
                        .build(),
                1002L,
                ServiceModel.newBuilder()
                        .setServiceId(2L)
                        .setType(ServiceType.SERVICE)
                        .setName("Service 2")
                        .build(),
                1003L,
                ServiceModel.newBuilder()
                        .setServiceId(3L)
                        .setType(ServiceType.ADDON)
                        .setName("AddOn 3")
                        .build()));
        obPetLimitFilterDTO.setCurrentServiceMap(Map.of(1001L, 1L, 1003L, 1L));

        OBPetDataDTO petData1 = new OBPetDataDTO();
        petData1.setWeight("10");
        petData1.setPetTypeId(1);
        petData1.setBreed("Doodle");
        obPetLimitFilterDTO.setPetIndexMap(Map.of(0, petData1));

        Integer staffId = 57001;
        String date = "2022-10-28";
        Integer startTime = null;

        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup = new LimitGroupDTO();
        limitGroup.setOnlyAcceptSelected(true);
        // 添加服务限制
        List<ServiceLimitDto> serviceLimitList = new ArrayList<>();
        serviceLimitList.add(ServiceLimitDto.builder()
                .serviceIds(List.of(1001L))
                .isAllService(false)
                .capacity(1)
                .build());
        limitGroup.setServiceLimitList(serviceLimitList);
        limitGroups.add(limitGroup);

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isTrue();
        assertThat(result.getPetAvailableSubList()).isEqualTo(petIndexSubList);
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withOneLimitation_acceptSelected_false() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = List.of(Set.of(0));
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);
        obPetLimitFilterDTO.setPetAppointmentMap(Map.of());
        obPetLimitFilterDTO.setPetIdDetailMap(Map.of());
        obPetLimitFilterDTO.setPetBreedMap(Map.of(
                40001,
                        MoePetBreedDTO.builder()
                                .id(40001)
                                .name("Doodle")
                                .petTypeId(1)
                                .build(),
                40002,
                        MoePetBreedDTO.builder()
                                .id(40002)
                                .name("Poodle")
                                .petTypeId(1)
                                .build(),
                40003,
                        MoePetBreedDTO.builder()
                                .id(40003)
                                .name("Golden Doodle")
                                .petTypeId(1)
                                .build()));
        obPetLimitFilterDTO.setPetSizeMap(Map.of(
                30001L,
                        PetSizeDTO.builder()
                                .id(1L)
                                .name("Small")
                                .weightLow(0)
                                .weightHigh(5)
                                .build(),
                30002L,
                        PetSizeDTO.builder()
                                .id(2L)
                                .name("Medium")
                                .weightLow(6)
                                .weightHigh(10)
                                .build(),
                30003L,
                        PetSizeDTO.builder()
                                .id(3L)
                                .name("Large")
                                .weightLow(11)
                                .weightHigh(15)
                                .build()));
        obPetLimitFilterDTO.setServiceMap(Map.of(
                1001L,
                        ServiceModel.newBuilder()
                                .setServiceId(1L)
                                .setName("Service 1")
                                .build(),
                1002L,
                        ServiceModel.newBuilder()
                                .setServiceId(2L)
                                .setName("Service 2")
                                .build()));

        OBPetDataDTO petData1 = new OBPetDataDTO();
        petData1.setWeight("10");
        petData1.setPetTypeId(1);
        petData1.setBreed("Golden Doodle");
        obPetLimitFilterDTO.setPetIndexMap(Map.of(0, petData1));

        Integer staffId = 57001;
        String date = "2022-10-28";
        Integer startTime = null;

        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup = new LimitGroupDTO();
        limitGroup.setOnlyAcceptSelected(true);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList = new ArrayList<>();
        petBreedLimitList.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40001L, 40002L))
                .isAllBreed(false)
                .capacity(1)
                .build());
        limitGroup.setPetBreedLimitList(petBreedLimitList);
        limitGroups.add(limitGroup);

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isFalse();
        assertThat(result.getPetAvailableSubList()).isNotEqualTo(petIndexSubList);
        assertThat(result.getPetSubListToTimeSlotTypeMap())
                .containsExactlyInAnyOrderEntriesOf(
                        Map.of(Set.of(0), List.of(TimeSlotType.PET_BREED_LIMITATION_NOT_MET)));
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withTwoLimitations_false() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = List.of(Set.of(0));
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);
        obPetLimitFilterDTO.setPetAppointmentMap(Map.of());
        obPetLimitFilterDTO.setPetIdDetailMap(Map.of());
        obPetLimitFilterDTO.setPetBreedMap(Map.of(
                40001,
                        MoePetBreedDTO.builder()
                                .id(40001)
                                .name("Doodle")
                                .petTypeId(1)
                                .build(),
                40002,
                        MoePetBreedDTO.builder()
                                .id(40002)
                                .name("Poodle")
                                .petTypeId(1)
                                .build(),
                40003,
                        MoePetBreedDTO.builder()
                                .id(40003)
                                .name("Golden Doodle")
                                .petTypeId(1)
                                .build()));
        obPetLimitFilterDTO.setPetSizeMap(Map.of(
                30001L,
                        PetSizeDTO.builder()
                                .id(1L)
                                .name("Small")
                                .weightLow(0)
                                .weightHigh(5)
                                .build(),
                30002L,
                        PetSizeDTO.builder()
                                .id(2L)
                                .name("Medium")
                                .weightLow(6)
                                .weightHigh(10)
                                .build(),
                30003L,
                        PetSizeDTO.builder()
                                .id(3L)
                                .name("Large")
                                .weightLow(11)
                                .weightHigh(15)
                                .build()));
        obPetLimitFilterDTO.setServiceMap(Map.of(
                1001L,
                        ServiceModel.newBuilder()
                                .setServiceId(1L)
                                .setName("Service 1")
                                .build(),
                1002L,
                        ServiceModel.newBuilder()
                                .setServiceId(2L)
                                .setName("Service 2")
                                .build()));

        OBPetDataDTO petData1 = new OBPetDataDTO();
        petData1.setWeight("10");
        petData1.setPetTypeId(1);
        petData1.setBreed("Doodle");
        obPetLimitFilterDTO.setPetIndexMap(Map.of(0, petData1));

        Integer staffId = 57001;
        String date = "2022-10-28";
        Integer startTime = null;

        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup = new LimitGroupDTO();
        limitGroup.setOnlyAcceptSelected(false);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList = new ArrayList<>();
        petBreedLimitList.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40001L, 40002L))
                .isAllBreed(false)
                .capacity(1)
                .build());
        limitGroup.setPetBreedLimitList(petBreedLimitList);
        // 添加宠物大小限制
        List<PetSizeLimitDto> petSizeLimitList = new ArrayList<>();
        petSizeLimitList.add(PetSizeLimitDto.builder()
                .petSizeIds(List.of(30002L))
                .isAllPetSize(false)
                .capacity(0)
                .build());
        limitGroup.setPetSizeLimitList(petSizeLimitList);
        limitGroups.add(limitGroup);

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isFalse();
        assertThat(result.getPetAvailableSubList()).isNotEqualTo(petIndexSubList);
        assertThat(result.getPetSubListToTimeSlotTypeMap())
                .containsExactlyInAnyOrderEntriesOf(
                        Map.of(Set.of(0), List.of(TimeSlotType.PET_SIZE_LIMITATION_NOT_MET)));
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withThreeLimitations_false() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = List.of(Set.of(0));
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);
        obPetLimitFilterDTO.setPetAppointmentMap(Map.of());
        obPetLimitFilterDTO.setPetIdDetailMap(Map.of());
        obPetLimitFilterDTO.setPetBreedMap(Map.of(
                40001,
                        MoePetBreedDTO.builder()
                                .id(40001)
                                .name("Doodle")
                                .petTypeId(1)
                                .build(),
                40002,
                        MoePetBreedDTO.builder()
                                .id(40002)
                                .name("Poodle")
                                .petTypeId(1)
                                .build(),
                40003,
                        MoePetBreedDTO.builder()
                                .id(40003)
                                .name("Golden Doodle")
                                .petTypeId(1)
                                .build()));
        obPetLimitFilterDTO.setPetSizeMap(Map.of(
                30001L,
                        PetSizeDTO.builder()
                                .id(1L)
                                .name("Small")
                                .weightLow(0)
                                .weightHigh(5)
                                .build(),
                30002L,
                        PetSizeDTO.builder()
                                .id(2L)
                                .name("Medium")
                                .weightLow(6)
                                .weightHigh(10)
                                .build(),
                30003L,
                        PetSizeDTO.builder()
                                .id(3L)
                                .name("Large")
                                .weightLow(11)
                                .weightHigh(15)
                                .build()));
        obPetLimitFilterDTO.setServiceMap(Map.of(
                1001L,
                        ServiceModel.newBuilder()
                                .setServiceId(1L)
                                .setName("Service 1")
                                .build(),
                1002L,
                        ServiceModel.newBuilder()
                                .setServiceId(2L)
                                .setName("Service 2")
                                .build()));
        obPetLimitFilterDTO.setCurrentServiceMap(Map.of(1001L, 2L));

        OBPetDataDTO petData1 = new OBPetDataDTO();
        petData1.setWeight("10");
        petData1.setPetTypeId(1);
        petData1.setBreed("Doodle");
        obPetLimitFilterDTO.setPetIndexMap(Map.of(0, petData1));

        Integer staffId = 57001;
        String date = "2022-10-28";
        Integer startTime = null;

        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup = new LimitGroupDTO();
        limitGroup.setOnlyAcceptSelected(false);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList = new ArrayList<>();
        petBreedLimitList.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40001L, 40002L))
                .isAllBreed(false)
                .capacity(1)
                .build());
        limitGroup.setPetBreedLimitList(petBreedLimitList);
        // 添加宠物大小限制
        List<PetSizeLimitDto> petSizeLimitList = new ArrayList<>();
        petSizeLimitList.add(PetSizeLimitDto.builder()
                .petSizeIds(List.of(30002L))
                .isAllPetSize(false)
                .capacity(1)
                .build());
        limitGroup.setPetSizeLimitList(petSizeLimitList);
        // 添加服务限制
        List<ServiceLimitDto> serviceLimitList = new ArrayList<>();
        serviceLimitList.add(ServiceLimitDto.builder()
                .serviceIds(List.of(1001L, 1002L))
                .isAllService(false)
                .capacity(1)
                .build());
        limitGroup.setServiceLimitList(serviceLimitList);
        limitGroups.add(limitGroup);

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isFalse();
        assertThat(result.getPetAvailableSubList()).isNotEqualTo(petIndexSubList);
        assertThat(result.getPetSubListToTimeSlotTypeMap()).isNotEmpty();
        assertThat(result.getPetSubListToTimeSlotTypeMap())
                .containsExactlyInAnyOrderEntriesOf(
                        Map.of(Set.of(0), List.of(TimeSlotType.SERVICE_LIMITATION_NOT_MET)));
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withTwoLimitations_withOneGroup_true() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = List.of(Set.of(0));
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);
        obPetLimitFilterDTO.setPetAppointmentMap(Map.of());
        obPetLimitFilterDTO.setPetIdDetailMap(Map.of());
        obPetLimitFilterDTO.setPetBreedMap(Map.of(
                40001,
                        MoePetBreedDTO.builder()
                                .id(40001)
                                .name("Doodle")
                                .petTypeId(1)
                                .build(),
                40002,
                        MoePetBreedDTO.builder()
                                .id(40002)
                                .name("Poodle")
                                .petTypeId(1)
                                .build(),
                40003,
                        MoePetBreedDTO.builder()
                                .id(40003)
                                .name("Golden Doodle")
                                .petTypeId(1)
                                .build()));
        obPetLimitFilterDTO.setPetSizeMap(Map.of(
                30001L,
                        PetSizeDTO.builder()
                                .id(1L)
                                .name("Small")
                                .weightLow(0)
                                .weightHigh(5)
                                .build(),
                30002L,
                        PetSizeDTO.builder()
                                .id(2L)
                                .name("Medium")
                                .weightLow(6)
                                .weightHigh(10)
                                .build(),
                30003L,
                        PetSizeDTO.builder()
                                .id(3L)
                                .name("Large")
                                .weightLow(11)
                                .weightHigh(15)
                                .build()));
        obPetLimitFilterDTO.setServiceMap(Map.of(
                1001L,
                        ServiceModel.newBuilder()
                                .setServiceId(1L)
                                .setName("Service 1")
                                .build(),
                1002L,
                        ServiceModel.newBuilder()
                                .setServiceId(2L)
                                .setName("Service 2")
                                .build()));
        obPetLimitFilterDTO.setCurrentServiceMap(Map.of(1001L, 2L));

        OBPetDataDTO petData1 = new OBPetDataDTO();
        petData1.setWeight("10");
        petData1.setPetTypeId(1);
        petData1.setBreed("Golden Doodle");
        obPetLimitFilterDTO.setPetIndexMap(Map.of(0, petData1));

        Integer staffId = 57001;
        String date = "2022-10-28";
        Integer startTime = null;

        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup1 = new LimitGroupDTO();
        limitGroup1.setOnlyAcceptSelected(false);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList1 = new ArrayList<>();
        petBreedLimitList1.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40001L, 40002L))
                .isAllBreed(false)
                .capacity(0)
                .build());
        limitGroup1.setPetBreedLimitList(petBreedLimitList1);
        limitGroups.add(limitGroup1);
        LimitGroupDTO limitGroup2 = new LimitGroupDTO();
        limitGroup2.setOnlyAcceptSelected(false);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList2 = new ArrayList<>();
        petBreedLimitList2.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40003L))
                .isAllBreed(false)
                .capacity(1)
                .build());
        limitGroup2.setPetBreedLimitList(petBreedLimitList2);
        limitGroups.add(limitGroup2);

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isTrue();
        assertThat(result.getPetAvailableSubList()).isEqualTo(petIndexSubList);
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withTwoLimitations_withOneGroup_allZero_true() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = List.of(Set.of(0));
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);
        obPetLimitFilterDTO.setPetAppointmentMap(Map.of());
        obPetLimitFilterDTO.setPetIdDetailMap(Map.of());
        obPetLimitFilterDTO.setPetBreedMap(Map.of(
                40001,
                        MoePetBreedDTO.builder()
                                .id(40001)
                                .name("Doodle")
                                .petTypeId(1)
                                .build(),
                40002,
                        MoePetBreedDTO.builder()
                                .id(40002)
                                .name("Poodle")
                                .petTypeId(1)
                                .build(),
                40003,
                        MoePetBreedDTO.builder()
                                .id(40003)
                                .name("Golden Doodle")
                                .petTypeId(1)
                                .build()));
        obPetLimitFilterDTO.setPetSizeMap(Map.of(
                30001L,
                        PetSizeDTO.builder()
                                .id(1L)
                                .name("Small")
                                .weightLow(0)
                                .weightHigh(5)
                                .build(),
                30002L,
                        PetSizeDTO.builder()
                                .id(2L)
                                .name("Medium")
                                .weightLow(6)
                                .weightHigh(10)
                                .build(),
                30003L,
                        PetSizeDTO.builder()
                                .id(3L)
                                .name("Large")
                                .weightLow(11)
                                .weightHigh(15)
                                .build()));
        obPetLimitFilterDTO.setServiceMap(Map.of(
                1001L,
                        ServiceModel.newBuilder()
                                .setServiceId(1L)
                                .setName("Service 1")
                                .build(),
                1002L,
                        ServiceModel.newBuilder()
                                .setServiceId(2L)
                                .setName("Service 2")
                                .build()));
        obPetLimitFilterDTO.setCurrentServiceMap(Map.of(1001L, 2L));

        OBPetDataDTO petData1 = new OBPetDataDTO();
        petData1.setWeight("10");
        petData1.setPetTypeId(1);
        petData1.setBreed("Golden Doodle");
        obPetLimitFilterDTO.setPetIndexMap(Map.of(0, petData1));

        Integer staffId = 57001;
        String date = "2022-10-28";
        Integer startTime = null;

        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup1 = new LimitGroupDTO();
        limitGroup1.setOnlyAcceptSelected(false);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList1 = new ArrayList<>();
        petBreedLimitList1.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40001L, 40002L))
                .isAllBreed(false)
                .capacity(0)
                .build());
        limitGroup1.setPetBreedLimitList(petBreedLimitList1);
        limitGroups.add(limitGroup1);
        LimitGroupDTO limitGroup2 = new LimitGroupDTO();
        limitGroup2.setOnlyAcceptSelected(false);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList2 = new ArrayList<>();
        petBreedLimitList2.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40003L))
                .isAllBreed(false)
                .capacity(0)
                .build());
        limitGroup2.setPetBreedLimitList(petBreedLimitList2);
        limitGroups.add(limitGroup2);

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isTrue();
        assertThat(result.getPetAvailableSubList()).isEqualTo(petIndexSubList);
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withTwoLimitations_withTwoGroups_acceptSelected_false() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = List.of(Set.of(0));
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);
        obPetLimitFilterDTO.setPetAppointmentMap(Map.of());
        obPetLimitFilterDTO.setPetIdDetailMap(Map.of());
        obPetLimitFilterDTO.setPetBreedMap(Map.of(
                40001,
                        MoePetBreedDTO.builder()
                                .id(40001)
                                .name("Doodle")
                                .petTypeId(1)
                                .build(),
                40002,
                        MoePetBreedDTO.builder()
                                .id(40002)
                                .name("Poodle")
                                .petTypeId(1)
                                .build(),
                40003,
                        MoePetBreedDTO.builder()
                                .id(40003)
                                .name("Golden Doodle")
                                .petTypeId(1)
                                .build()));
        obPetLimitFilterDTO.setPetSizeMap(Map.of(
                30001L,
                        PetSizeDTO.builder()
                                .id(1L)
                                .name("Small")
                                .weightLow(0)
                                .weightHigh(5)
                                .build(),
                30002L,
                        PetSizeDTO.builder()
                                .id(2L)
                                .name("Medium")
                                .weightLow(6)
                                .weightHigh(10)
                                .build(),
                30003L,
                        PetSizeDTO.builder()
                                .id(3L)
                                .name("Large")
                                .weightLow(11)
                                .weightHigh(15)
                                .build()));
        obPetLimitFilterDTO.setServiceMap(Map.of(
                1001L,
                        ServiceModel.newBuilder()
                                .setServiceId(1L)
                                .setName("Service 1")
                                .build(),
                1002L,
                        ServiceModel.newBuilder()
                                .setServiceId(2L)
                                .setName("Service 2")
                                .build()));
        obPetLimitFilterDTO.setCurrentServiceMap(Map.of(1001L, 2L));

        OBPetDataDTO petData1 = new OBPetDataDTO();
        petData1.setWeight("10");
        petData1.setPetTypeId(1);
        petData1.setBreed("Golden Doodle");
        obPetLimitFilterDTO.setPetIndexMap(Map.of(0, petData1));

        Integer staffId = 57001;
        String date = "2022-10-28";
        Integer startTime = null;

        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup1 = new LimitGroupDTO();
        limitGroup1.setOnlyAcceptSelected(true);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList1 = new ArrayList<>();
        petBreedLimitList1.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40001L, 40002L))
                .isAllBreed(false)
                .capacity(0)
                .build());
        limitGroup1.setPetBreedLimitList(petBreedLimitList1);
        limitGroups.add(limitGroup1);
        LimitGroupDTO limitGroup2 = new LimitGroupDTO();
        limitGroup2.setOnlyAcceptSelected(false);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList2 = new ArrayList<>();
        petBreedLimitList2.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40003L))
                .isAllBreed(false)
                .capacity(0)
                .build());
        limitGroup2.setPetBreedLimitList(petBreedLimitList2);
        limitGroups.add(limitGroup2);

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isFalse();
        assertThat(result.getPetAvailableSubList()).isNotEqualTo(petIndexSubList);
        assertThat(result.getPetSubListToTimeSlotTypeMap())
                .containsExactlyInAnyOrderEntriesOf(
                        Map.of(Set.of(0), List.of(TimeSlotType.PET_BREED_LIMITATION_NOT_MET)));
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withTwoPets_withTwoLimitations_withTwoGroups_acceptSelected_true() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = List.of(Set.of(0, 1));
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);
        obPetLimitFilterDTO.setPetAppointmentMap(Map.of());
        obPetLimitFilterDTO.setPetIdDetailMap(Map.of());
        obPetLimitFilterDTO.setPetBreedMap(Map.of(
                40001,
                        MoePetBreedDTO.builder()
                                .id(40001)
                                .name("Doodle")
                                .petTypeId(1)
                                .build(),
                40002,
                        MoePetBreedDTO.builder()
                                .id(40002)
                                .name("Poodle")
                                .petTypeId(1)
                                .build(),
                40003,
                        MoePetBreedDTO.builder()
                                .id(40003)
                                .name("Golden Doodle")
                                .petTypeId(1)
                                .build()));
        obPetLimitFilterDTO.setPetSizeMap(Map.of(
                30001L,
                        PetSizeDTO.builder()
                                .id(1L)
                                .name("Small")
                                .weightLow(0)
                                .weightHigh(5)
                                .build(),
                30002L,
                        PetSizeDTO.builder()
                                .id(2L)
                                .name("Medium")
                                .weightLow(6)
                                .weightHigh(10)
                                .build(),
                30003L,
                        PetSizeDTO.builder()
                                .id(3L)
                                .name("Large")
                                .weightLow(11)
                                .weightHigh(15)
                                .build()));
        obPetLimitFilterDTO.setServiceMap(Map.of(
                1001L,
                        ServiceModel.newBuilder()
                                .setServiceId(1L)
                                .setName("Service 1")
                                .build(),
                1002L,
                        ServiceModel.newBuilder()
                                .setServiceId(2L)
                                .setName("Service 2")
                                .build()));
        obPetLimitFilterDTO.setCurrentServiceMap(Map.of(1001L, 2L));

        OBPetDataDTO petData1 = new OBPetDataDTO();
        petData1.setWeight("10");
        petData1.setPetTypeId(1);
        petData1.setBreed("Doodle");
        OBPetDataDTO petData2 = new OBPetDataDTO();
        petData2.setWeight("10");
        petData2.setPetTypeId(1);
        petData2.setBreed("Poodle");
        obPetLimitFilterDTO.setPetIndexMap(Map.of(0, petData1, 1, petData2));

        Integer staffId = 57001;
        String date = "2022-10-28";
        Integer startTime = null;

        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup1 = new LimitGroupDTO();
        limitGroup1.setOnlyAcceptSelected(true);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList1 = new ArrayList<>();
        petBreedLimitList1.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40001L))
                .isAllBreed(false)
                .capacity(1)
                .build());
        petBreedLimitList1.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40002L))
                .isAllBreed(false)
                .capacity(1)
                .build());
        limitGroup1.setPetBreedLimitList(petBreedLimitList1);
        limitGroups.add(limitGroup1);
        LimitGroupDTO limitGroup2 = new LimitGroupDTO();
        limitGroup2.setOnlyAcceptSelected(true);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList2 = new ArrayList<>();
        petBreedLimitList2.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40001L))
                .isAllBreed(false)
                .capacity(2)
                .build());
        limitGroup2.setPetBreedLimitList(petBreedLimitList2);
        limitGroups.add(limitGroup2);

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isTrue();
        assertThat(result.getPetAvailableSubList()).isEqualTo(petIndexSubList);
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withTwoPets_withTwoLimitations_withTwoGroups_acceptSelected_false() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = List.of(Set.of(0, 1));
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);
        obPetLimitFilterDTO.setPetAppointmentMap(Map.of());
        obPetLimitFilterDTO.setPetIdDetailMap(Map.of());
        obPetLimitFilterDTO.setPetBreedMap(Map.of(
                40001,
                        MoePetBreedDTO.builder()
                                .id(40001)
                                .name("Doodle")
                                .petTypeId(1)
                                .build(),
                40002,
                        MoePetBreedDTO.builder()
                                .id(40002)
                                .name("Poodle")
                                .petTypeId(1)
                                .build(),
                40003,
                        MoePetBreedDTO.builder()
                                .id(40003)
                                .name("Golden Doodle")
                                .petTypeId(1)
                                .build()));
        obPetLimitFilterDTO.setPetSizeMap(Map.of(
                30001L,
                        PetSizeDTO.builder()
                                .id(1L)
                                .name("Small")
                                .weightLow(0)
                                .weightHigh(5)
                                .build(),
                30002L,
                        PetSizeDTO.builder()
                                .id(2L)
                                .name("Medium")
                                .weightLow(6)
                                .weightHigh(10)
                                .build(),
                30003L,
                        PetSizeDTO.builder()
                                .id(3L)
                                .name("Large")
                                .weightLow(11)
                                .weightHigh(15)
                                .build()));
        obPetLimitFilterDTO.setServiceMap(Map.of(
                1001L,
                        ServiceModel.newBuilder()
                                .setServiceId(1L)
                                .setName("Service 1")
                                .build(),
                1002L,
                        ServiceModel.newBuilder()
                                .setServiceId(2L)
                                .setName("Service 2")
                                .build()));
        obPetLimitFilterDTO.setCurrentServiceMap(Map.of(1001L, 2L));

        OBPetDataDTO petData1 = new OBPetDataDTO();
        petData1.setWeight("10");
        petData1.setPetTypeId(1);
        petData1.setBreed("Doodle");
        OBPetDataDTO petData2 = new OBPetDataDTO();
        petData2.setWeight("10");
        petData2.setPetTypeId(1);
        petData2.setBreed("Poodle");
        obPetLimitFilterDTO.setPetIndexMap(Map.of(0, petData1, 1, petData2));

        Integer staffId = 57001;
        String date = "2022-10-28";
        Integer startTime = null;

        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup1 = new LimitGroupDTO();
        limitGroup1.setOnlyAcceptSelected(true);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList1 = new ArrayList<>();
        petBreedLimitList1.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40001L))
                .isAllBreed(false)
                .capacity(1)
                .build());
        petBreedLimitList1.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40002L))
                .isAllBreed(false)
                .capacity(0)
                .build());
        limitGroup1.setPetBreedLimitList(petBreedLimitList1);
        limitGroups.add(limitGroup1);
        LimitGroupDTO limitGroup2 = new LimitGroupDTO();
        limitGroup2.setOnlyAcceptSelected(true);
        // 添加宠物品种限制
        List<PetBreedLimitDto> petBreedLimitList2 = new ArrayList<>();
        petBreedLimitList2.add(PetBreedLimitDto.builder()
                .petTypeId(1L)
                .breedIds(List.of(40001L))
                .isAllBreed(false)
                .capacity(2)
                .build());
        limitGroup2.setPetBreedLimitList(petBreedLimitList2);
        limitGroups.add(limitGroup2);

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isFalse();
        assertThat(result.getPetAvailableSubList()).isNotEqualTo(petIndexSubList);
        assertThat(result.getPetSubListToTimeSlotTypeMap())
                .containsExactlyInAnyOrderEntriesOf(
                        Map.of(Set.of(0, 1), List.of(TimeSlotType.PET_BREED_LIMITATION_NOT_MET)));
    }

    @Test
    void testJudgePetLimitAndGetPetRemainQuantity_withMultiPetMultiStaff_withServiceLimitation_true() {
        // 准备测试数据
        OBPetLimitFilterDTO obPetLimitFilterDTO = new OBPetLimitFilterDTO();
        List<Set<Integer>> petIndexSubList = List.of(Set.of(0));
        obPetLimitFilterDTO.setPetIndexSubList(petIndexSubList);
        obPetLimitFilterDTO.setPetAppointmentMap(Map.of());
        obPetLimitFilterDTO.setPetIdDetailMap(Map.of());
        obPetLimitFilterDTO.setPetBreedMap(Map.of(
                40001,
                MoePetBreedDTO.builder().id(40001).name("Doodle").petTypeId(1).build(),
                40002,
                MoePetBreedDTO.builder().id(40002).name("Poodle").petTypeId(1).build(),
                40003,
                MoePetBreedDTO.builder()
                        .id(40003)
                        .name("Golden Doodle")
                        .petTypeId(1)
                        .build(),
                40011,
                MoePetBreedDTO.builder()
                        .id(40011)
                        .name("Abyssinian")
                        .petTypeId(2)
                        .build()));
        obPetLimitFilterDTO.setPetSizeMap(Map.of(
                30001L,
                PetSizeDTO.builder()
                        .id(1L)
                        .name("Small")
                        .weightLow(0)
                        .weightHigh(5)
                        .build(),
                30002L,
                PetSizeDTO.builder()
                        .id(2L)
                        .name("Medium")
                        .weightLow(6)
                        .weightHigh(10)
                        .build(),
                30003L,
                PetSizeDTO.builder()
                        .id(3L)
                        .name("Large")
                        .weightLow(11)
                        .weightHigh(15)
                        .build()));
        obPetLimitFilterDTO.setServiceMap(Map.of(
                1001L,
                ServiceModel.newBuilder()
                        .setServiceId(1L)
                        .setType(ServiceType.SERVICE)
                        .setName("Service 1")
                        .build(),
                1002L,
                ServiceModel.newBuilder()
                        .setServiceId(2L)
                        .setType(ServiceType.SERVICE)
                        .setName("Service 2")
                        .build(),
                1003L,
                ServiceModel.newBuilder()
                        .setServiceId(3L)
                        .setType(ServiceType.SERVICE)
                        .setName("Service 3")
                        .build()));
        obPetLimitFilterDTO.setCurrentServiceMap(Map.of(1001L, 1L, 1002L, 1L, 1003L, 1L));
        obPetLimitFilterDTO.setCurrentStaffServiceMap(
                Map.of(57001, Map.of(1001L, 1L, 1002L, 1L), 57002, Map.of(1002L, 1L, 1003L, 1L)));

        OBPetDataDTO petData1 = new OBPetDataDTO();
        petData1.setWeight("10");
        petData1.setPetTypeId(1);
        petData1.setBreed("Doodle");
        obPetLimitFilterDTO.setPetIndexMap(Map.of(0, petData1));

        Integer staffId = 57001;
        String date = "2022-10-28";
        Integer startTime = null;

        List<LimitGroupDTO> limitGroups = new ArrayList<>();

        LimitGroupDTO limitGroup = new LimitGroupDTO();
        limitGroup.setOnlyAcceptSelected(true);
        // 添加服务限制
        List<ServiceLimitDto> serviceLimitList = new ArrayList<>();
        serviceLimitList.add(ServiceLimitDto.builder()
                .serviceIds(List.of(1001L))
                .isAllService(false)
                .capacity(1)
                .build());
        serviceLimitList.add(ServiceLimitDto.builder()
                .serviceIds(List.of(1002L))
                .isAllService(false)
                .capacity(1)
                .build());
        limitGroup.setServiceLimitList(serviceLimitList);
        limitGroups.add(limitGroup);

        // 执行测试
        PetAvailableDTO result = OBPetLimitService.judgePetLimitAndGetPetRemainQuantity(
                obPetLimitFilterDTO, staffId, date, limitGroups, startTime, petIndexSubList);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getIsAllPetsAvailable()).isTrue();
        assertThat(result.getPetAvailableSubList()).isEqualTo(petIndexSubList);
    }

    @Nested
    @DisplayName("isServiceNoMoreQuantity 方法测试")
    class JudgeServiceQuantityTest {

        @Test
        void shouldReturnFalseWhenLimitServiceListMapIsNull() {
            List<Map<Set<Long>, Integer>> limitServiceListMap = null;
            var currentServiceMap = Map.of(1L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnFalseWhenLimitServiceListMapIsEmpty() {
            var limitServiceListMap = List.<Map<Set<Long>, Integer>>of();
            var currentServiceMap = Map.of(1L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnTrueWhenAllGroupsHaveNegativeQuantity0() {
            // service A = 1，service B = 1，已经有一个 service A 和 B 了，那么再来一个 service A，noMoreServiceQuantity 为 false
            var limitServiceListMap = List.of(Map.of(Set.of(1L), 0), Map.of(Set.of(2L), 0));
            var currentServiceMap = Map.of(1L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnTrueWhenAllGroupsHaveNegativeQuantity() {
            // service A = 2，service B = 2，已经有一个 service A 和 B 了，那么再来一个 service A，noMoreServiceQuantity 为 false
            var limitServiceListMap = List.of(Map.of(Set.of(1L), 1), Map.of(Set.of(2L), 1));
            var currentServiceMap = Map.of(1L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnTrueWhenAllGroupsHaveNegativeQuantity2() {
            // service A max = 2，service B max = 2，已经有一个 service A 和 B 了，那么再来一个 service A，noMoreServiceQuantity 为 true
            var limitServiceListMap =
                    List.of(Map.of(Set.of(1L), 1, Set.of(2L), -1), Map.of(Set.of(2L), 1, Set.of(1L), -1));
            var currentServiceMap = Map.of(1L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = true;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnTrueWhenAllGroupsAreFilteredOut() {
            // service A max = 1，service B max = 2，已经有一个 service B 了，那么再来一个 service A，noMoreServiceQuantity 为 true
            var limitServiceListMap =
                    List.of(Map.of(Set.of(1L), 1, Set.of(2L), -1), Map.of(Set.of(2L), 1, Set.of(1L), 0));
            var currentServiceMap = Map.of(1L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = true;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnFalseWhenServiceQuantityExceedsLimit() {
            // service A max = 2，service B max = 2，已经有一个 service A 和 B 了，那么再来一个 service A，noMoreServiceQuantity 为 false
            var limitServiceListMap = List.of(Map.of(Set.of(1L), 1), Map.of(Set.of(2L), 1));
            var currentServiceMap = Map.of(1L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnFalseWhenServiceQuantityExceedsLimit1() {
            // service A max =1，service B max = 1，再来一个 service A 和 B，noMoreServiceQuantity 为 false
            var limitServiceListMap =
                    List.of(Map.of(Set.of(1L), 1, Set.of(2L), 1), Map.of(Set.of(1L), 2, Set.of(2L), 0));
            var currentServiceMap = Map.of(1L, 1L, 2L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnFalseWhenServiceQuantityWithinLimit() {
            // service A = 2，service B = 2，已经有一个 service A 和 B 了，那么再来一个 service A 和 B，noMoreServiceQuantity 为 false
            var limitServiceListMap = List.of(Map.of(Set.of(1L), 1), Map.of(Set.of(2L), 1));
            var currentServiceMap = Map.of(1L, 1L, 2L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnTrueWhenMultipleServicesInGroupExceedLimit() {
            // service A = 2，service B max = 2，已经有一个 service A 和 B 了，那么再来一个 service A，noMoreServiceQuantity 为 false
            var limitServiceListMap = List.of(Map.of(Set.of(1L), 1), Map.of(Set.of(2L), 1, Set.of(1L), -1));
            var currentServiceMap = Map.of(1L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnFalseWhenMultipleServicesInGroupWithinLimit() {
            // service A = 2，service B max = 2，已经有一个 service A 和 B 了，那么再来一个 service B，noMoreServiceQuantity 为 false
            var limitServiceListMap = List.of(Map.of(Set.of(1L), 1), Map.of(Set.of(2L), 1, Set.of(1L), -1));
            var currentServiceMap = Map.of(2L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnFalseWhenMultipleServicesInGroupWithinLimit2() {
            // service A = 2，service B max = 2，已经有一个 service A 和 B 了，那么再来一个 service C，noMoreServiceQuantity 为 false
            var limitServiceListMap =
                    List.of(Map.of(Set.of(1L), 1), Map.of(Set.of(2L), 1, Set.of(1L), -1, Set.of(3L), 0));
            var currentServiceMap = Map.of(3L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnTrueWhenAnyGroupExceedsLimit() {
            // service A = 2，service B max = 2，已经有一个 service B 了，那么再来一个 service B，noMoreServiceQuantity 为 false
            var limitServiceListMap = List.of(Map.of(Set.of(1L), 2), Map.of(Set.of(2L), 1, Set.of(1L), 0));
            var currentServiceMap = Map.of(2L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnFalseWhenAllGroupsWithinLimit() {
            // service A = 2，service B max = 2，已经有一个 service B 了，那么再来一个 service A，noMoreServiceQuantity 为 true
            var limitServiceListMap = List.of(Map.of(Set.of(1L), 2), Map.of(Set.of(2L), 1, Set.of(1L), 0));
            var currentServiceMap = Map.of(1L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnFalseWhenAllGroupsWithinLimit2() {
            // service A = 2，service B max = 2，已经有一个 service B 了，那么再来一个 service C，noMoreServiceQuantity 为 true
            var limitServiceListMap =
                    List.of(Map.of(Set.of(1L), 2), Map.of(Set.of(2L), 1, Set.of(1L), 0, Set.of(3L), 0));
            var currentServiceMap = Map.of(3L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnFalseWhenCurrentServiceMapIsEmpty() {
            // service A max = 2，service B max = 2，service A max = 2 and service B max = 2，已经有一个 service A 和 B 了，那么再来一个
            // service A，noMoreServiceQuantity 为 false
            var limitServiceListMap = List.of(
                    Map.of(Set.of(1L), 1, Set.of(2L), -1),
                    Map.of(Set.of(2L), 1, Set.of(1L), -1),
                    Map.of(Set.of(1L), 1, Set.of(2L), 1));
            var currentServiceMap = Map.of(1L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldReturnFalseWhenServiceNotInLimitGroup() {
            // service A max = 2，service B max = 2，service A max = 2 and service B max = 2，已经有一个 service A 和 B 了，那么再来一个
            // service A 和 B，noMoreServiceQuantity 为 false
            var limitServiceListMap = List.of(
                    Map.of(Set.of(1L), 1, Set.of(2L), -1),
                    Map.of(Set.of(2L), 1, Set.of(1L), -1),
                    Map.of(Set.of(1L), 1, Set.of(2L), 1));
            var currentServiceMap = Map.of(1L, 1L, 2L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = false;
            assertThat(actual).isEqualTo(expected);
        }

        @Test
        void shouldHandleComplexScenarioWithMultipleGroupsAndServices() {
            // service A max = 2，service B max = 2，service A max = 2 and service B max = 2，已经有一个 service A 和 B 了，那么再来一个
            // service C，noMoreServiceQuantity 为 true
            var limitServiceListMap = List.of(
                    Map.of(Set.of(1L), 1, Set.of(2L), -1, Set.of(3L), 0),
                    Map.of(Set.of(2L), 1, Set.of(1L), -1, Set.of(3L), 0),
                    Map.of(Set.of(1L), 1, Set.of(2L), 1, Set.of(3L), 0));
            var currentServiceMap = Map.of(3L, 1L);

            var actual = OBPetLimitService.isServiceNoMoreQuantity(limitServiceListMap, currentServiceMap);

            var expected = true;
            assertThat(actual).isEqualTo(expected);
        }
    }
}
